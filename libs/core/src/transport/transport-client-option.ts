import type { RedisOptions, GrpcOptions } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import type { EnvService } from '../env/env.service';
import { join } from 'path';

export function getRedisTransportOption(envService: EnvService) {
  const host = envService.get('REDIS_HOST');
  const port = envService.get('REDIS_PORT');
  const username = envService.get('REDIS_USERNAME');
  const password = envService.get('REDIS_PASSWORD');

  return {
    transport: Transport.REDIS,
    options: {
      host,
      port,
      username,
      password,
    },
  } as RedisOptions;
}

/**
 * Get gRPC transport configuration
 *
 * @param envService Environment service for configuration
 * @param serviceName Name of the service for proto file resolution
 * @returns gRPC transport options
 */
export function getGrpcTransportOption(envService: EnvService, serviceName: string = 'datastore') {
  const host = envService.get('GRPC_HOST') || 'localhost';
  const port = envService.get('GRPC_PORT') || 5000;

  // For now, we'll use a simple gRPC configuration without proto files
  // In a full implementation, you would specify proto files here
  return {
    transport: Transport.GRPC,
    options: {
      url: `${host}:${port}`,
      package: 'patterntrade',
      // protoPath: join(__dirname, `../../../proto/${serviceName}.proto`),
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  } as GrpcOptions;
}

/**
 * Get dual transport configuration (Redis + gRPC)
 *
 * @param envService Environment service for configuration
 * @param serviceName Name of the service for proto file resolution
 * @returns Array of transport options
 */
export function getDualTransportOptions(envService: EnvService, serviceName: string = 'datastore') {
  return [
    getRedisTransportOption(envService),
    getGrpcTransportOption(envService, serviceName),
  ];
}
