import { Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Observable, firstValueFrom, timeout, catchError, of } from 'rxjs';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import {
  SymbolServiceMessagePatterns,
  SymbolServiceSchemas,
  SymbolMasterQueryFilters,
  SymbolDownloadRequest,
  SymbolMaster,
} from './symbol.schema';

/**
 * Symbol gRPC-style Service Client
 *
 * Provides a high-level client interface for communicating with the datastore
 * symbol service using Redis transport in a gRPC-like manner. Handles request/response
 * patterns, error handling, timeouts, and retry logic.
 *
 * Features:
 * - Type-safe request/response handling
 * - Automatic timeout and retry logic
 * - Comprehensive error handling
 * - Observable-based communication
 * - Request validation using Zod schemas
 * - Response transformation and validation
 */
@Injectable()
export class SymbolGrpcService {
  private readonly logger = new Logger(SymbolGrpcService.name);
  private readonly defaultTimeout = 30000; // 30 seconds
  private readonly retryAttempts = 3;

  constructor(
    private readonly client: ClientProxy,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== SYMBOL DATA OPERATIONS ====================

  /**
   * Get symbols with filters and pagination
   */
  async getSymbols(filters: SymbolMasterQueryFilters): Promise<{
    success: boolean;
    data: SymbolMaster[];
    meta: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    timestamp: Date;
  }> {
    try {
      this.logger.log('Requesting symbols from datastore service', filters);

      // Validate request
      const validatedFilters = SymbolServiceSchemas.GetSymbolsRequest.parse(filters);

      // Send request with timeout
      const response = await this.sendRequest(SymbolServiceMessagePatterns.GET_SYMBOLS, validatedFilters);

      // Validate response
      const validatedResponse = SymbolServiceSchemas.GetSymbolsResponse.parse(response);

      this.logger.log('Symbols retrieved successfully', {
        count: validatedResponse.data.length,
        total: validatedResponse.meta.total,
      });

      return validatedResponse;
    } catch (error) {
      this.logger.error('Failed to get symbols from datastore service', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'getSymbols');
    }
  }

  /**
   * Get symbol by instrument token
   */
  async getSymbolByToken(instrumentToken: string): Promise<{
    success: boolean;
    data: SymbolMaster | null;
    timestamp: Date;
  }> {
    try {
      this.logger.log('Requesting symbol by token from datastore service', { instrumentToken });

      // Validate request
      const request = SymbolServiceSchemas.GetSymbolByTokenRequest.parse({ instrumentToken });

      // Send request with timeout
      const response = await this.sendRequest(SymbolServiceMessagePatterns.GET_SYMBOL_BY_TOKEN, request);

      // Validate response
      const validatedResponse = SymbolServiceSchemas.GetSymbolByTokenResponse.parse(response);

      this.logger.log('Symbol retrieved successfully', {
        instrumentToken,
        found: !!validatedResponse.data,
      });

      return validatedResponse;
    } catch (error) {
      this.logger.error('Failed to get symbol by token from datastore service', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'getSymbolByToken');
    }
  }

  /**
   * Search symbols by trading symbol pattern
   */
  async searchSymbols(
    searchTerm: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<{
    success: boolean;
    data: SymbolMaster[];
    meta: {
      searchTerm: string;
      limit: number;
      offset: number;
      count: number;
    };
    timestamp: Date;
  }> {
    try {
      this.logger.log('Searching symbols in datastore service', { searchTerm, limit, offset });

      // Create search filters
      const filters: SymbolMasterQueryFilters = {
        tradingSymbol: searchTerm,
        limit,
        offset,
      };

      // Use getSymbols with search filters
      const response = await this.getSymbols(filters);

      return {
        success: response.success,
        data: response.data,
        meta: {
          searchTerm,
          limit,
          offset,
          count: response.data.length,
        },
        timestamp: response.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to search symbols in datastore service', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'searchSymbols');
    }
  }

  // ==================== DOWNLOAD OPERATIONS ====================

  /**
   * Trigger symbol download
   */
  async triggerDownload(request: SymbolDownloadRequest): Promise<{
    success: boolean;
    data: {
      jobId: string;
      requestId: string;
      scheduledAt: Date;
    };
    timestamp: Date;
  }> {
    try {
      this.logger.log('Triggering symbol download in datastore service', request);

      // Validate request
      const validatedRequest = SymbolServiceSchemas.TriggerDownloadRequest.parse(request);

      // Send request with timeout
      const response = await this.sendRequest(SymbolServiceMessagePatterns.TRIGGER_DOWNLOAD, validatedRequest);

      // Validate response
      const validatedResponse = SymbolServiceSchemas.TriggerDownloadResponse.parse(response);

      this.logger.log('Symbol download triggered successfully', {
        jobId: validatedResponse.data.jobId,
        requestId: validatedResponse.data.requestId,
      });

      return validatedResponse;
    } catch (error) {
      this.logger.error('Failed to trigger symbol download in datastore service', {
        request,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'triggerDownload');
    }
  }

  /**
   * Get download status
   */
  async getDownloadStatus(jobId: string): Promise<{
    success: boolean;
    data: any;
    timestamp: Date;
  }> {
    try {
      this.logger.log('Getting download status from datastore service', { jobId });

      // Validate request
      const request = SymbolServiceSchemas.GetDownloadStatusRequest.parse({ jobId });

      // Send request with timeout
      const response = await this.sendRequest(SymbolServiceMessagePatterns.GET_DOWNLOAD_STATUS, request);

      // Validate response
      const validatedResponse = SymbolServiceSchemas.GetDownloadStatusResponse.parse(response);

      this.logger.log('Download status retrieved successfully', {
        jobId,
        status: validatedResponse.data.status,
      });

      return validatedResponse;
    } catch (error) {
      this.logger.error('Failed to get download status from datastore service', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'getDownloadStatus');
    }
  }

  // ==================== HEALTH AND STATUS OPERATIONS ====================

  /**
   * Get service health status
   */
  async getHealth(): Promise<{
    success: boolean;
    data: {
      isHealthy: boolean;
      services: any;
    };
    timestamp: Date;
  }> {
    try {
      this.logger.log('Getting health status from datastore service');

      // Send request with shorter timeout for health checks
      const response = await this.sendRequest(
        SymbolServiceMessagePatterns.GET_HEALTH,
        {},
        10000, // 10 second timeout for health checks
      );

      this.logger.log('Health status retrieved successfully', {
        isHealthy: response.data?.isHealthy,
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to get health status from datastore service', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'getHealth');
    }
  }

  /**
   * Get service statistics
   */
  async getStats(): Promise<{
    success: boolean;
    data: any;
    timestamp: Date;
  }> {
    try {
      this.logger.log('Getting statistics from datastore service');

      // Send request with timeout
      const response = await this.sendRequest(SymbolServiceMessagePatterns.GET_STATS, {});

      this.logger.log('Statistics retrieved successfully');

      return response;
    } catch (error) {
      this.logger.error('Failed to get statistics from datastore service', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleServiceError(error, 'getStats');
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Send request to datastore service with timeout and error handling
   */
  private async sendRequest(pattern: string, data: any, timeoutMs: number = this.defaultTimeout): Promise<any> {
    try {
      const observable = this.client.send(pattern, data).pipe(
        timeout(timeoutMs),
        catchError((error) => {
          this.logger.error('Request failed', {
            pattern,
            error: this.errorUtils.getErrorMessage(error),
          });
          throw error;
        }),
      );

      const response = await firstValueFrom(observable);

      if (!response.success) {
        throw new Error(response.error || 'Request failed');
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to send request', {
        pattern,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Handle service errors consistently
   */
  private handleServiceError(error: unknown, operation: string): Error {
    const errorMessage = this.errorUtils.getErrorMessage(error);

    if (errorMessage.includes('timeout')) {
      return new Error(`Datastore service timeout during ${operation}: ${errorMessage}`);
    }

    if (errorMessage.includes('connection')) {
      return new Error(`Datastore service connection error during ${operation}: ${errorMessage}`);
    }

    return new Error(`Datastore service error during ${operation}: ${errorMessage}`);
  }

  /**
   * Check if the datastore service is available
   */
  async isServiceAvailable(): Promise<boolean> {
    try {
      await this.getHealth();
      return true;
    } catch (error) {
      this.logger.warn('Datastore service is not available', {
        error: this.errorUtils.getErrorMessage(error),
      });
      return false;
    }
  }

  /**
   * Wait for service to become available
   */
  async waitForService(maxWaitMs: number = 30000): Promise<boolean> {
    const startTime = Date.now();
    const checkInterval = 1000; // Check every second

    while (Date.now() - startTime < maxWaitMs) {
      if (await this.isServiceAvailable()) {
        this.logger.log('Datastore service is now available');
        return true;
      }

      await new Promise((resolve) => setTimeout(resolve, checkInterval));
    }

    this.logger.error('Datastore service did not become available within timeout', {
      maxWaitMs,
    });
    return false;
  }
}
