import type { FactoryProvider } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxyFactory } from '@nestjs/microservices';
import { EnvService } from '@app/core/env';
import { ErrorUtilsService } from '@app/utils';
import { getRedisTransportOption } from '@app/core/transport';
import { RedisTransportConfigSchema } from '@app/core/client';
import { constant } from '@app/common';

/**
 * Symbol Service Client Provider
 *
 * Creates a Redis transport client for communicating with the datastore
 * symbol service. Provides proper configuration validation and error handling.
 *
 * Features:
 * - Redis transport configuration
 * - Environment-based configuration
 * - Configuration validation with Zod
 * - Error handling for connection issues
 * - Dependency injection support
 */
export const SymbolServiceClientProvider: FactoryProvider = {
  provide: constant.SYMBOL_SERVICE_CLIENT,
  useFactory: (envService: EnvService, errorUtils: ErrorUtilsService) => {
    try {
      // Get the Redis transport options
      const transportOptions = getRedisTransportOption(envService);

      // Validate the transport configuration with Zod
      RedisTransportConfigSchema.parse(transportOptions.options);

      // Create and return the client proxy
      return ClientProxyFactory.create(transportOptions);
    } catch (error) {
      throw new Error(
        `Invalid Redis transport configuration for Symbol service client: ${errorUtils.getErrorMessage(error)}`,
      );
    }
  },
  inject: [EnvService, ErrorUtilsService],
};

/**
 * Symbol Service Client Injection Decorator
 *
 * Convenience decorator for injecting the Symbol service client
 *
 * @example
 * ```typescript
 * constructor(
 *   @SymbolServiceClient() private readonly symbolClient: ClientProxy
 * ) {}
 * ```
 */
export function SymbolServiceClient() {
  return Inject(constant.SYMBOL_SERVICE_CLIENT);
}

/**
 * Symbol Service Client Configuration
 *
 * Configuration object for the Symbol service client with default values
 * and environment-specific overrides.
 */
export const SymbolServiceClientConfig = {
  // Connection settings
  connectionTimeout: 30000, // 30 seconds
  requestTimeout: 60000, // 60 seconds for download operations
  retryAttempts: 3,
  retryDelay: 1000, // 1 second

  // Health check settings
  healthCheckInterval: 30000, // 30 seconds
  healthCheckTimeout: 10000, // 10 seconds

  // Queue settings
  maxConcurrentRequests: 10,
  queueTimeout: 300000, // 5 minutes

  // Service discovery
  serviceName: 'datastore',
  serviceVersion: '1.0.0',
} as const;

/**
 * Symbol Service Client Health Check
 *
 * Utility function to check if the Symbol service client is healthy
 * and can communicate with the datastore service.
 */
export async function checkSymbolServiceHealth(client: any): Promise<{
  isHealthy: boolean;
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();

  try {
    // Send a simple ping to check connectivity
    const response = await client.send('datastore.ping', {}).toPromise();
    const latency = Date.now() - startTime;

    if (response && response.success) {
      return {
        isHealthy: true,
        latency,
      };
    } else {
      return {
        isHealthy: false,
        error: 'Invalid response from service',
      };
    }
  } catch (error) {
    return {
      isHealthy: false,
      latency: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Symbol Service Client Metrics
 *
 * Interface for tracking Symbol service client metrics
 */
export interface SymbolServiceClientMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  lastRequestTime: Date | null;
  connectionStatus: 'connected' | 'disconnected' | 'error';
  errorRate: number;
}

/**
 * Symbol Service Client Metrics Tracker
 *
 * Simple metrics tracking for the Symbol service client
 */
export class SymbolServiceClientMetricsTracker {
  private metrics: SymbolServiceClientMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    lastRequestTime: null,
    connectionStatus: 'disconnected',
    errorRate: 0,
  };

  private latencies: number[] = [];
  private readonly maxLatencyHistory = 100;

  /**
   * Record a successful request
   */
  recordSuccess(latency: number): void {
    this.metrics.totalRequests++;
    this.metrics.successfulRequests++;
    this.metrics.lastRequestTime = new Date();
    this.metrics.connectionStatus = 'connected';

    this.updateLatency(latency);
    this.updateErrorRate();
  }

  /**
   * Record a failed request
   */
  recordFailure(latency?: number): void {
    this.metrics.totalRequests++;
    this.metrics.failedRequests++;
    this.metrics.lastRequestTime = new Date();
    this.metrics.connectionStatus = 'error';

    if (latency !== undefined) {
      this.updateLatency(latency);
    }

    this.updateErrorRate();
  }

  /**
   * Get current metrics
   */
  getMetrics(): SymbolServiceClientMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  reset(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      lastRequestTime: null,
      connectionStatus: 'disconnected',
      errorRate: 0,
    };
    this.latencies = [];
  }

  /**
   * Update average latency
   */
  private updateLatency(latency: number): void {
    this.latencies.push(latency);

    // Keep only the last N latencies
    if (this.latencies.length > this.maxLatencyHistory) {
      this.latencies.shift();
    }

    // Calculate average
    this.metrics.averageLatency = this.latencies.reduce((sum, lat) => sum + lat, 0) / this.latencies.length;
  }

  /**
   * Update error rate
   */
  private updateErrorRate(): void {
    if (this.metrics.totalRequests > 0) {
      this.metrics.errorRate = (this.metrics.failedRequests / this.metrics.totalRequests) * 100;
    }
  }
}

/**
 * Symbol Service Client Factory
 *
 * Factory function for creating Symbol service clients with custom configuration
 */
export function createSymbolServiceClient(envService: EnvService, config?: Partial<typeof SymbolServiceClientConfig>) {
  const finalConfig = { ...SymbolServiceClientConfig, ...config };
  const transportOptions = getRedisTransportOption(envService);

  // Apply custom configuration
  if (finalConfig.connectionTimeout) {
    transportOptions.options.connectTimeout = finalConfig.connectionTimeout;
  }

  return ClientProxyFactory.create(transportOptions);
}

/**
 * Symbol Service Client Module Configuration
 *
 * Configuration for importing the Symbol service client in modules
 */
export const SymbolServiceClientModule = {
  providers: [SymbolServiceClientProvider],
  exports: [SymbolServiceClientProvider],
} as const;
