import { Module } from '@nestjs/common';
import { SymbolService } from './symbol.service';
import { SymbolDownloadQueueService } from './symbol-download.queue';
import { SymbolDownloadWorkerService } from './symbol-download.worker';
import { SymbolMasterRepository } from './symbol-master.repository';
import { SymbolAuditService } from './symbol-audit.service';
import { SymbolGrpcService } from './symbol-grpc.service';
import { SymbolServiceClientProvider } from './symbol-client.provider';

/**
 * Symbol Module
 *
 * Provides comprehensive symbol master data management functionality including:
 * - Symbol data storage and retrieval with QuestDB
 * - Background job processing with BullMQ
 * - Audit logging with PostgreSQL
 * - gRPC-style microservice communication
 * - Real-time data synchronization with Kite Connect API
 *
 * Features:
 * - Symbol master data repository
 * - Download queue and worker services
 * - Audit logging and compliance
 * - Microservice communication
 * - Health monitoring and statistics
 */
@Module({
  providers: [
    // Core services
    SymbolService,
    SymbolMasterRepository,
    SymbolAuditService,

    // Queue and worker services
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,

    // gRPC-style communication
    SymbolGrpcService,
    SymbolServiceClientProvider,
  ],
  exports: [
    // Core services
    SymbolService,
    SymbolMasterRepository,
    SymbolAuditService,

    // Queue and worker services
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,

    // gRPC-style communication
    SymbolGrpcService,
    SymbolServiceClientProvider,
  ],
})
export class SymbolModule {}
