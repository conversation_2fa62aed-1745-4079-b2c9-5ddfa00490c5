{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/api/tsconfig.app.json"}, "projects": {"admin": {"type": "library", "root": "libs/admin", "entryFile": "index", "sourceRoot": "libs/admin/src", "compilerOptions": {"tsConfigPath": "libs/admin/tsconfig.lib.json"}}, "analyser": {"type": "application", "root": "apps/analyser", "entryFile": "main", "sourceRoot": "apps/analyser/src", "compilerOptions": {"tsConfigPath": "apps/analyser/tsconfig.app.json"}}, "api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "core": {"type": "library", "root": "libs/core", "entryFile": "index", "sourceRoot": "libs/core/src", "compilerOptions": {"tsConfigPath": "libs/core/tsconfig.lib.json"}}, "datastore": {"type": "application", "root": "apps/datastore", "entryFile": "main", "sourceRoot": "apps/datastore/src", "compilerOptions": {"tsConfigPath": "apps/datastore/tsconfig.app.json"}}, "event": {"type": "library", "root": "libs/event", "entryFile": "index", "sourceRoot": "libs/event/src", "compilerOptions": {"tsConfigPath": "libs/event/tsconfig.lib.json"}}, "holding": {"type": "library", "root": "libs/holding", "entryFile": "index", "sourceRoot": "libs/holding/src", "compilerOptions": {"tsConfigPath": "libs/holding/tsconfig.lib.json"}}, "journal": {"type": "library", "root": "libs/journal", "entryFile": "index", "sourceRoot": "libs/journal/src", "compilerOptions": {"tsConfigPath": "libs/journal/tsconfig.lib.json"}}, "margin": {"type": "library", "root": "libs/margin", "entryFile": "index", "sourceRoot": "libs/margin/src", "compilerOptions": {"tsConfigPath": "libs/margin/tsconfig.lib.json"}}, "notification": {"type": "library", "root": "libs/notification", "entryFile": "index", "sourceRoot": "libs/notification/src", "compilerOptions": {"tsConfigPath": "libs/notification/tsconfig.lib.json"}}, "oms": {"type": "application", "root": "apps/oms", "entryFile": "main", "sourceRoot": "apps/oms/src", "compilerOptions": {"tsConfigPath": "apps/oms/tsconfig.app.json"}}, "order": {"type": "library", "root": "libs/order", "entryFile": "index", "sourceRoot": "libs/order/src", "compilerOptions": {"tsConfigPath": "libs/order/tsconfig.lib.json"}}, "portfolio": {"type": "library", "root": "libs/portfolio", "entryFile": "index", "sourceRoot": "libs/portfolio/src", "compilerOptions": {"tsConfigPath": "libs/portfolio/tsconfig.lib.json"}}, "position": {"type": "library", "root": "libs/position", "entryFile": "index", "sourceRoot": "libs/position/src", "compilerOptions": {"tsConfigPath": "libs/position/tsconfig.lib.json"}}, "scanner": {"type": "library", "root": "libs/scanner", "entryFile": "index", "sourceRoot": "libs/scanner/src", "compilerOptions": {"tsConfigPath": "libs/scanner/tsconfig.lib.json"}}, "schedular": {"type": "library", "root": "libs/schedular", "entryFile": "index", "sourceRoot": "libs/schedular/src", "compilerOptions": {"tsConfigPath": "libs/schedular/tsconfig.lib.json"}}, "screener": {"type": "application", "root": "apps/screener", "entryFile": "main", "sourceRoot": "apps/screener/src", "compilerOptions": {"tsConfigPath": "apps/screener/tsconfig.app.json"}}, "signal": {"type": "library", "root": "libs/signal", "entryFile": "index", "sourceRoot": "libs/signal/src", "compilerOptions": {"tsConfigPath": "libs/signal/tsconfig.lib.json"}}, "simulator": {"type": "application", "root": "apps/simulator", "entryFile": "main", "sourceRoot": "apps/simulator/src", "compilerOptions": {"tsConfigPath": "apps/simulator/tsconfig.app.json"}}, "stats": {"type": "library", "root": "libs/stats", "entryFile": "index", "sourceRoot": "libs/stats/src", "compilerOptions": {"tsConfigPath": "libs/stats/tsconfig.lib.json"}}, "strategy": {"type": "library", "root": "libs/strategy", "entryFile": "index", "sourceRoot": "libs/strategy/src", "compilerOptions": {"tsConfigPath": "libs/strategy/tsconfig.lib.json"}}, "symbol": {"type": "library", "root": "libs/symbol", "entryFile": "index", "sourceRoot": "libs/symbol/src", "compilerOptions": {"tsConfigPath": "libs/symbol/tsconfig.lib.json"}}, "ticker": {"type": "application", "root": "apps/ticker", "entryFile": "main", "sourceRoot": "apps/ticker/src", "compilerOptions": {"tsConfigPath": "apps/ticker/tsconfig.app.json"}}, "ticker-data": {"type": "library", "root": "libs/ticker-data", "entryFile": "index", "sourceRoot": "libs/ticker-data/src", "compilerOptions": {"tsConfigPath": "libs/ticker-data/tsconfig.lib.json"}}, "trade": {"type": "library", "root": "libs/trade", "entryFile": "index", "sourceRoot": "libs/trade/src", "compilerOptions": {"tsConfigPath": "libs/trade/tsconfig.lib.json"}}, "utils": {"type": "library", "root": "libs/utils", "entryFile": "index", "sourceRoot": "libs/utils/src", "compilerOptions": {"tsConfigPath": "libs/utils/tsconfig.lib.json"}}, "watchlist": {"type": "library", "root": "libs/watchlist", "entryFile": "index", "sourceRoot": "libs/watchlist/src", "compilerOptions": {"tsConfigPath": "libs/watchlist/tsconfig.lib.json"}}}, "monorepo": true, "root": "apps/api"}