import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Logger,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { SymbolService } from './symbol.service';
import {
  SymbolQueryDto,
  SymbolDownloadDto,
  SymbolSearchDto,
  SymbolResponseDto,
  SymbolDownloadResponseDto,
  SymbolStatusResponseDto,
} from './symbol.dto';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

/**
 * Symbol Controller for API Gateway
 *
 * Provides REST API endpoints for symbol master data operations.
 * Acts as the gateway between external clients and the datastore microservice.
 *
 * Features:
 * - Symbol data querying and searching
 * - Symbol download triggering and monitoring
 * - Job status tracking
 * - Authentication and authorization
 * - Comprehensive error handling
 * - API documentation with Swagger
 */
@ApiTags('symbols')
@Controller('symbols')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SymbolController {
  private readonly logger = new Logger(SymbolController.name);

  constructor(
    private readonly symbolService: SymbolService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== SYMBOL DATA ENDPOINTS ====================

  /**
   * Get symbols with filters and pagination
   */
  @Get()
  @ApiOperation({
    summary: 'Get symbols with filters',
    description: 'Retrieve symbol master data with optional filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Symbols retrieved successfully',
    type: SymbolResponseDto,
  })
  @ApiQuery({ name: 'exchange', required: false, description: 'Filter by exchange (NSE, BSE, etc.)' })
  @ApiQuery({ name: 'segment', required: false, description: 'Filter by segment' })
  @ApiQuery({ name: 'instrumentType', required: false, description: 'Filter by instrument type' })
  @ApiQuery({ name: 'tradingSymbol', required: false, description: 'Filter by trading symbol pattern' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Filter by active status' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum results to return (default: 100)' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip (default: 0)' })
  async getSymbols(@Query() query: SymbolQueryDto): Promise<SymbolResponseDto> {
    try {
      this.logger.log('Getting symbols with filters', query);

      const result = await this.symbolService.getSymbols(query);

      return {
        success: true,
        data: result.data,
        meta: result.meta,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbols', {
        query,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get symbol by instrument token
   */
  @Get(':instrumentToken')
  @ApiOperation({
    summary: 'Get symbol by instrument token',
    description: 'Retrieve a specific symbol by its instrument token',
  })
  @ApiParam({ name: 'instrumentToken', description: 'Instrument token of the symbol' })
  @ApiResponse({
    status: 200,
    description: 'Symbol retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Symbol not found',
  })
  async getSymbolByToken(@Param('instrumentToken') instrumentToken: string) {
    try {
      this.logger.log('Getting symbol by token', { instrumentToken });

      const symbol = await this.symbolService.getSymbolByToken(instrumentToken);

      if (!symbol.data) {
        throw new HttpException(
          {
            success: false,
            error: 'Symbol not found',
            timestamp: this.dateTimeUtils.getUtcNow(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: symbol.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('Failed to get symbol by token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Search symbols by trading symbol pattern
   */
  @Get('search/:searchTerm')
  @ApiOperation({
    summary: 'Search symbols',
    description: 'Search symbols by trading symbol pattern',
  })
  @ApiParam({ name: 'searchTerm', description: 'Search term for trading symbol' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum results to return (default: 50)' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip (default: 0)' })
  @ApiResponse({
    status: 200,
    description: 'Symbols found successfully',
  })
  async searchSymbols(@Param('searchTerm') searchTerm: string, @Query() query: SymbolSearchDto) {
    try {
      this.logger.log('Searching symbols', { searchTerm, ...query });

      const result = await this.symbolService.searchSymbols(searchTerm, query.limit || 50, query.offset || 0);

      return {
        success: true,
        data: result.data,
        meta: {
          searchTerm,
          limit: query.limit || 50,
          offset: query.offset || 0,
          count: result.data.length,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        query,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== DOWNLOAD OPERATIONS ====================

  /**
   * Trigger symbol download
   */
  @Post('download')
  @ApiOperation({
    summary: 'Trigger symbol download',
    description: 'Trigger a symbol master data download from Zerodha Kite Connect API',
  })
  @ApiResponse({
    status: 201,
    description: 'Download triggered successfully',
    type: SymbolDownloadResponseDto,
  })
  async triggerDownload(@Body() downloadDto: SymbolDownloadDto): Promise<SymbolDownloadResponseDto> {
    try {
      this.logger.log('Triggering symbol download', downloadDto);

      const result = await this.symbolService.triggerDownload(downloadDto);

      return {
        success: true,
        data: {
          jobId: result.data.jobId,
          requestId: result.data.requestId,
          scheduledAt: result.data.scheduledAt,
          estimatedCompletion: result.estimatedCompletion,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger symbol download', {
        downloadDto,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get download job status
   */
  @Get('download/status/:jobId')
  @ApiOperation({
    summary: 'Get download status',
    description: 'Get the status of a symbol download job',
  })
  @ApiParam({ name: 'jobId', description: 'Job ID of the download operation' })
  @ApiResponse({
    status: 200,
    description: 'Status retrieved successfully',
    type: SymbolStatusResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Job not found',
  })
  async getDownloadStatus(@Param('jobId') jobId: string): Promise<SymbolStatusResponseDto> {
    try {
      this.logger.log('Getting download status', { jobId });

      const status = await this.symbolService.getDownloadStatus(jobId);

      if (!status.data) {
        throw new HttpException(
          {
            success: false,
            error: 'Job not found',
            timestamp: this.dateTimeUtils.getUtcNow(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: status.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('Failed to get download status', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Trigger immediate download for all exchanges
   */
  @Post('download/immediate')
  @ApiOperation({
    summary: 'Trigger immediate download',
    description: 'Trigger an immediate symbol download for all exchanges with high priority',
  })
  @ApiResponse({
    status: 201,
    description: 'Immediate download triggered successfully',
  })
  async triggerImmediateDownload() {
    try {
      this.logger.log('Triggering immediate download for all exchanges');

      const result = await this.symbolService.triggerImmediateDownload();

      return {
        success: true,
        data: {
          jobId: result.jobId,
          requestId: result.requestId,
          scheduledAt: result.scheduledAt,
          priority: 'HIGH',
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger immediate download', {
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== STATISTICS AND HEALTH ====================

  /**
   * Get symbol service statistics
   */
  @Get('stats')
  @ApiOperation({
    summary: 'Get symbol statistics',
    description: 'Get comprehensive statistics about the symbol service',
  })
  @ApiResponse({
    status: 200,
    description: 'Statistics retrieved successfully',
  })
  async getStats() {
    try {
      this.logger.log('Getting symbol service statistics');

      const stats = await this.symbolService.getStats();

      return {
        success: true,
        data: stats.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbol statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get symbol service health
   */
  @Get('health')
  @ApiOperation({
    summary: 'Get symbol service health',
    description: 'Get health status of the symbol service',
  })
  @ApiResponse({
    status: 200,
    description: 'Health status retrieved successfully',
  })
  async getHealth() {
    try {
      this.logger.log('Getting symbol service health');

      const health = await this.symbolService.getHealth();

      return {
        success: true,
        data: health.data,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbol service health', {
        error: this.errorUtils.getErrorMessage(error),
      });

      throw new HttpException(
        {
          success: false,
          error: this.errorUtils.getErrorMessage(error),
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
