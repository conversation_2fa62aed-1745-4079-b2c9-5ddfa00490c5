import { Module } from '@nestjs/common';
import { SymbolController } from './symbol.controller';
import { SymbolService } from './symbol.service';
import { SymbolServiceClientProvider } from '@app/symbol';

// Core modules
import { UtilsModule } from '@app/utils';

/**
 * Symbol Module for API Gateway
 *
 * Provides REST API endpoints for symbol master data operations.
 * Communicates with the datastore microservice for actual data operations.
 *
 * Features:
 * - REST API endpoints for symbol operations
 * - Microservice communication with datastore
 * - Request validation and transformation
 * - Error handling and logging
 * - Authentication and authorization
 * - API documentation with Swagger
 */
@Module({
  imports: [UtilsModule],
  controllers: [SymbolController],
  providers: [SymbolService, SymbolServiceClientProvider],
  exports: [SymbolService],
})
export class SymbolModule {}
