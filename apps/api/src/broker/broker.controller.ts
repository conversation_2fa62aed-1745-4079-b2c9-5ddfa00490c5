import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  Req,
  UseFilters,
} from '@nestjs/common';
import { Throttle, ThrottlerGuard } from '@nestjs/throttler';
import { Request } from 'express';
import { AuthGuard, RolesGuard, Roles } from '@app/auth';
import { RoleEnum, Role } from '@app/auth/constants/role.enum';
import { RequireIPWhitelist, IPWhitelistGuard, AuditLoggingService } from '@app/common/security';
import {
  IPWhitelistOperationsEnum,
  AuditEventTypeEnum,
  AuditEventCategoryEnum,
  AuditEventSeverityEnum,
} from '@app/common/security/security.constants';
import { AllExceptionsFilter } from '@app/common/filters';
import { EncryptionService } from '@app/common/encryption';
import {
  BrokerService,
  BrokerAuthService,
  BrokerError,
  BrokerErrorEnum,
  type ConnectionHealth,
  type ConnectionStatistics,
} from '@app/broker';
import {
  CreateBrokerAccountDto,
  UpdateBrokerAccountDto,
  BrokerAccountFiltersDto,
  OAuthCallbackDto,
  AccountIdParamDto,
} from './broker.dto';
import type {
  BrokerAccountResponse,
  OAuthInitiationResponse,
  OAuthCallbackResponse,
  BrokerHealthResponse,
  ReconnectionResponse,
  SuccessResponse,
  DeleteSuccessResponse,
} from '@app/broker';
import { BrokerType, BrokerConnectionStatusEnum } from '@app/common/constants';
import { DateTimeUtilsService } from '@app/utils/datetime-utils.service';

// Define extended request interface for authenticated users
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: Role;
  };
}

/**
 * Broker controller providing REST API endpoints for broker account management
 *
 * Requirements:
 * - 1.1: Admin role validation for broker account operations
 * - 5.1: POST /api/broker/accounts endpoint
 * - 5.2: GET /api/broker/accounts endpoint
 * - 5.3: GET /api/broker/accounts/:id endpoint
 * - 5.4: PUT /api/broker/accounts/:id endpoint
 * - 5.5: DELETE /api/broker/accounts/:id endpoint
 * - 5.6: POST /api/broker/callback endpoint
 * - 5.7: GET /api/broker/health endpoint
 * - 5.8: POST /api/broker/reconnect/:id endpoint
 * - 6.2: Proper error handling with descriptive messages
 * - 7.2: Zod schemas for validation
 */
@Controller('broker')
@UseGuards(AuthGuard, RolesGuard, ThrottlerGuard, IPWhitelistGuard)
@UseFilters(AllExceptionsFilter)
export class BrokerController {
  private readonly logger = new Logger(BrokerController.name);

  constructor(
    private readonly brokerService: BrokerService,
    private readonly brokerAuthService: BrokerAuthService,
    // private readonly brokerMonitoringService: BrokerMonitoringService,
    private readonly auditService: AuditLoggingService,
    private readonly encryptionService: EncryptionService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  // ==================== BROKER ACCOUNT MANAGEMENT ENDPOINTS ====================

  /**
   * Create a new broker account
   * Requirements: 1.1, 5.1 - Admin-only access with proper validation
   */
  @Post('accounts')
  @Roles(RoleEnum.enum.admin)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 requests per minute
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.CREATE_BROKER_ACCOUNT)
  async createBrokerAccount(
    @Body() createData: CreateBrokerAccountDto,
    @Req() request: AuthenticatedRequest,
  ): Promise<BrokerAccountResponse> {
    const correlationId = this.auditService.generateCorrelationId();
    const userId = request.user?.id?.toString();

    if (!userId) {
      const error = new BrokerError(BrokerErrorEnum.enum.INSUFFICIENT_PERMISSIONS, 'BROKER', {
        message: 'User authentication required',
        context: {
          correlationId,
          operation: 'CREATE_ACCOUNT',
        },
        isRetryable: false,
        severity: AuditEventSeverityEnum.enum.HIGH,
      });

      // Log security audit event
      this.auditService.logSecurityEvent(
        AuditEventTypeEnum.enum.SECURITY_VIOLATION,
        userId || 'UNKNOWN',
        'CREATE_ACCOUNT',
        'Missing user authentication',
        correlationId,
        'BROKER',
        this.getClientIp(request),
        request.get('User-Agent'),
        { brokerType: createData.brokerType },
      );

      throw error;
    }

    const brokerAccount = await this.brokerService.createAccount(
      {
        userId,
        brokerType: createData.brokerType,
        accountName: createData.accountName,
        isAdminAccount: createData.isAdminAccount,
        credentials: createData.credentials,
      },
      correlationId,
    );

    // Log successful account creation
    this.auditService.logAuditEvent({
      eventType: AuditEventTypeEnum.enum.ACCOUNT_CREATED,
      userId,
      accountId: brokerAccount.id.toString(),
      details: {
        brokerType: createData.brokerType,
        accountName: createData.accountName,
        isAdminAccount: createData.isAdminAccount,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      severity: AuditEventSeverityEnum.enum.MEDIUM,
      category: AuditEventCategoryEnum.enum.OPERATION,
      module: 'BROKER',
    });

    return brokerAccount;
  }

  /**
   * Get all broker accounts with optional filtering
   * Requirements: 1.1, 5.2 - Admin-only access with filtering support
   */
  @Get('accounts')
  @Roles(RoleEnum.enum.admin)
  async getBrokerAccounts(@Query() filters: BrokerAccountFiltersDto): Promise<BrokerAccountResponse[]> {
    this.logger.log('Retrieving broker accounts with filters:', filters);

    const brokerAccounts = await this.brokerService.findAccountsWithFilters(filters);

    this.logger.log(`Retrieved ${brokerAccounts.length} broker accounts`);
    return brokerAccounts;
  }

  /**
   * Get a specific broker account by ID
   * Requirements: 1.1, 5.3 - Admin-only access for specific account retrieval
   */
  @Get('accounts/:id')
  @Roles(RoleEnum.enum.admin)
  async getBrokerAccount(@Param() params: AccountIdParamDto): Promise<BrokerAccountResponse> {
    this.logger.log(`Retrieving broker account with ID: ${params.id}`);

    const brokerAccount = await this.brokerService.findAccountById(params.id);

    if (!brokerAccount) {
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, 'BROKER', {
        message: `Broker account with ID ${params.id} not found`,
        context: { accountId: params.id.toString() },
      });
    }

    this.logger.log(`Successfully retrieved broker account with ID: ${params.id}`);
    return brokerAccount;
  }

  /**
   * Update a broker account
   * Requirements: 1.1, 5.4 - Admin-only access for account updates
   */
  @Put('accounts/:id')
  @Roles(RoleEnum.enum.admin)
  @Throttle({ default: { limit: 10, ttl: 300000 } }) // 10 requests per 5 minutes
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.UPDATE_BROKER_CREDENTIALS)
  async updateBrokerAccount(
    @Param() params: AccountIdParamDto,
    @Body() updateData: UpdateBrokerAccountDto,
  ): Promise<BrokerAccountResponse> {
    this.logger.log(`Updating broker account with ID: ${params.id}`);

    const updatedAccount = await this.brokerService.updateAccount(params.id, updateData);

    this.logger.log(`Successfully updated broker account with ID: ${params.id}`);
    return updatedAccount;
  }

  /**
   * Delete a broker account
   * Requirements: 1.1, 5.5 - Admin-only access for account deletion
   */
  @Delete('accounts/:id')
  @Roles(RoleEnum.enum.admin)
  @Throttle({ default: { limit: 3, ttl: 60000 } }) // 3 requests per minute
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.DELETE_BROKER_ACCOUNT)
  async deleteBrokerAccount(@Param() params: AccountIdParamDto): Promise<DeleteSuccessResponse> {
    this.logger.log(`Deleting broker account with ID: ${params.id}`);

    await this.brokerService.deleteAccount(params.id);

    this.logger.log(`Successfully deleted broker account with ID: ${params.id}`);
    return {
      success: true,
      message: `Broker account with ID ${params.id} deleted successfully`,
      timestamp: this.dateTimeUtils.getUtcNow(),
    };
  }

  // ==================== OAUTH AUTHENTICATION ENDPOINTS ====================

  /**
   * Initiate OAuth flow for a broker account
   * Requirements: 2.1, 2.5 - OAuth flow initiation with CSRF protection
   */
  @Post('oauth/initiate')
  @Roles(RoleEnum.enum.admin, RoleEnum.enum.user)
  @Throttle({ default: { limit: 20, ttl: 300000 } }) // 20 requests per 5 minutes
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.OAUTH_INITIATE)
  async initiateOAuth(
    @Body('accountId') accountId: number,
    @Req() request: AuthenticatedRequest,
  ): Promise<OAuthInitiationResponse> {
    this.logger.log(`Initiating OAuth flow for account: ${accountId}`);

    const userId = request.user?.id?.toString();
    if (!userId) {
      throw new BrokerError(BrokerErrorEnum.enum.INSUFFICIENT_PERMISSIONS, 'BROKER', {
        message: 'User authentication required',
      });
    }

    if (!accountId) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_BROKER_RESPONSE, 'BROKER', {
        message: 'Account ID is required',
      });
    }

    const oauthResponse = await this.brokerAuthService.initiateOAuth(accountId, userId);

    this.logger.log(`OAuth flow initiated successfully for account: ${accountId}`);
    return oauthResponse;
  }

  /**
   * Handle OAuth callback from broker
   * Requirements: 2.2, 2.3, 2.5, 5.6 - OAuth callback processing with token exchange
   *
   * This endpoint processes OAuth callbacks from supported brokers:
   * - Validates state parameter for CSRF protection
   * - Exchanges authorization code for access tokens using KiteConnect
   * - Securely stores tokens with encryption
   * - Provides comprehensive error handling and user feedback
   */
  @Post('callback')
  @Roles(RoleEnum.enum.admin, RoleEnum.enum.user)
  @Throttle({ default: { limit: 30, ttl: 300000 } }) // 30 requests per 5 minutes
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.OAUTH_CALLBACK)
  async handleOAuthCallback(
    @Body() callbackData: OAuthCallbackDto,
    @Req() request: AuthenticatedRequest,
  ): Promise<OAuthCallbackResponse> {
    const correlationId = this.auditService.generateCorrelationId();
    const userId = request.user?.id?.toString();

    this.logger.log(`Processing OAuth callback [${correlationId}]`, {
      correlationId,
      userId,
      hasCode: !!callbackData.code,
      hasState: !!callbackData.state,
      hasError: !!callbackData.error,
    });

    // Handle OAuth errors from the provider
    if (callbackData.error) {
      const errorMessage = callbackData.error_description || callbackData.error;
      const error = new BrokerError(BrokerErrorEnum.enum.OAUTH_FAILED, 'BROKER', {
        message: `OAuth authentication failed: ${errorMessage}. Please try the authentication process again.`,
        context: {
          correlationId,
          operation: 'OAUTH_CALLBACK',
          metadata: {
            oauthError: callbackData.error,
            oauthErrorDescription: callbackData.error_description,
          },
        },
        isRetryable: true,
        severity: AuditEventSeverityEnum.enum.MEDIUM,
      });

      // Log OAuth provider error
      this.logger.warn(`OAuth provider returned error [${correlationId}]:`, {
        correlationId,
        userId,
        error: callbackData.error,
        errorDescription: callbackData.error_description,
      });

      throw error;
    }

    // Enhanced error handling for missing authentication
    if (!userId) {
      const error = new BrokerError(BrokerErrorEnum.enum.INSUFFICIENT_PERMISSIONS, 'BROKER', {
        message: 'User authentication required for OAuth callback',
        context: {
          correlationId,
          operation: 'OAUTH_CALLBACK',
        },
        isRetryable: false,
        severity: AuditEventSeverityEnum.enum.HIGH,
      });

      // Log security audit event
      this.auditService.logSecurityEvent(
        AuditEventTypeEnum.enum.SECURITY_VIOLATION,
        userId || 'UNKNOWN',
        'OAUTH_CALLBACK',
        'Missing user authentication in OAuth callback',
        correlationId,
        'BROKER',
        this.getClientIp(request),
        request.get('User-Agent'),
      );

      throw error;
    }

    // Process OAuth callback with enhanced error handling
    const callbackResponse = await this.brokerAuthService.handleOAuthCallback(
      callbackData.code,
      callbackData.state,
      correlationId,
    );

    // Log successful OAuth completion
    this.logger.log(`OAuth callback processed successfully [${correlationId}]`, {
      correlationId,
      userId,
      accountId: callbackResponse.accountId,
      success: callbackResponse.success,
    });

    // Enhanced success response with additional context
    return {
      ...callbackResponse,
      message: callbackResponse.success
        ? `OAuth authentication completed successfully. Account ${callbackResponse.accountId} is now connected.`
        : callbackResponse.message,
    };
  }

  /**
   * Refresh access token for a broker account
   * Requirements: 2.4, 2.6 - Token refresh functionality
   */
  @Post('accounts/:id/refresh-token')
  @Roles(RoleEnum.enum.admin)
  @Throttle({ default: { limit: 50, ttl: 300000 } }) // 50 requests per 5 minutes
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.REFRESH_TOKEN)
  async refreshAccessToken(@Param() params: AccountIdParamDto): Promise<SuccessResponse> {
    this.logger.log(`Refreshing access token for account: ${params.id}`);

    await this.brokerAuthService.refreshAccessToken(params.id.toString());

    this.logger.log(`Access token refreshed successfully for account: ${params.id}`);
    return {
      success: true,
      message: `Access token refreshed successfully for account ${params.id}`,
      timestamp: this.dateTimeUtils.getUtcNow(),
    };
  }

  /**
   * Validate tokens for a broker account
   * Requirements: 2.6 - Token validation functionality
   */
  @Post('accounts/:id/validate-tokens')
  @Roles(RoleEnum.enum.admin)
  async validateTokens(
    @Param() params: AccountIdParamDto,
  ): Promise<{ valid: boolean; message: string; timestamp: string }> {
    this.logger.log(`Validating tokens for account: ${params.id}`);

    const isValid = await this.brokerAuthService.validateTokens(params.id.toString());

    this.logger.log(`Token validation result for account ${params.id}: ${isValid}`);
    return {
      valid: isValid,
      message: isValid ? 'Tokens are valid' : 'Tokens are invalid or expired',
      timestamp: this.dateTimeUtils.getUtcNow(),
    };
  }

  /**
   * Revoke tokens for a broker account
   * Requirements: OAuth token management
   */
  @Post('accounts/:id/revoke-tokens')
  @Roles(RoleEnum.enum.admin)
  @Throttle({ default: { limit: 20, ttl: 300000 } }) // 20 requests per 5 minutes
  @RequireIPWhitelist(IPWhitelistOperationsEnum.enum.REVOKE_TOKEN)
  async revokeTokens(@Param() params: AccountIdParamDto): Promise<SuccessResponse> {
    this.logger.log(`Revoking tokens for account: ${params.id}`);

    await this.brokerAuthService.revokeTokens(params.id.toString());

    this.logger.log(`Tokens revoked successfully for account: ${params.id}`);
    return {
      success: true,
      message: `Tokens revoked successfully for account ${params.id}`,
      timestamp: this.dateTimeUtils.getUtcNow(),
    };
  }

  // ==================== MONITORING AND DASHBOARD ENDPOINTS ====================

  /**
   * Get real-time monitoring dashboard data
   * Requirements: 3.1, 3.2, 3.3 - Real-time monitoring and dashboard visualization
   */
  // @Get('monitoring/dashboard')
  // @Roles('admin')
  // async getMonitoringDashboard(): Promise<MonitoringDashboardData> {
  //   try {
  //     this.logger.log('Retrieving monitoring dashboard data');

  //     const dashboardData = await this.brokerMonitoringService.getDashboardData();

  //     this.logger.log('Successfully retrieved monitoring dashboard data');
  //     return dashboardData;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  /**
   * Get real-time connection health for all accounts
   * Requirements: 3.1 - Real-time connection status monitoring
   */
  // @Get('monitoring/connections')
  // @Roles('admin')
  // async getRealTimeConnections(): Promise<ConnectionHealth[]> {
  //   try {
  //     this.logger.log('Retrieving real-time connection health');

  //     const connectionHealth = await this.brokerMonitoringService.getAllConnectionHealth();

  //     this.logger.log(`Retrieved real-time health for ${connectionHealth.length} connections`);
  //     return connectionHealth;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  /**
   * Get monitoring metrics
   * Requirements: 3.1, 3.3 - Performance tracking and metrics
   */
  // @Get('monitoring/metrics')
  // @Roles('admin')
  // async getMonitoringMetrics(): Promise<BrokerMonitoringMetrics> {
  //   try {
  //     this.logger.log('Retrieving monitoring metrics');

  //     const dashboardData = await this.brokerMonitoringService.getDashboardData();

  //     this.logger.log('Successfully retrieved monitoring metrics');
  //     return dashboardData.metrics;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  /**
   * Get recent alerts
   * Requirements: 3.2 - Alerting for broker connection failures
   */
  // @Get('monitoring/alerts')
  // @Roles('admin')
  // async getRecentAlerts(@Query('hours') hours?: string): Promise<AlertEvent[]> {
  //   try {
  //     const hoursNum = hours ? parseInt(hours, 10) : 24;
  //     this.logger.log(`Retrieving alerts for last ${hoursNum} hours`);

  //     const recentAlerts = await this.brokerMonitoringService.getRecentAlerts(hoursNum);

  //     this.logger.log(`Retrieved ${recentAlerts.length} recent alerts`);
  //     return recentAlerts;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  /**
   * Start real-time monitoring
   * Requirements: 3.1 - Real-time connection status monitoring
   */
  // @Post('monitoring/start')
  // @Roles('admin')
  // @RequireIPWhitelist('MONITORING_CONTROL')
  // async startMonitoring(): Promise<SuccessResponse> {
  //   try {
  //     this.logger.log('Starting real-time monitoring');

  //     this.brokerMonitoringService.startRealTimeMonitoring();

  //     this.logger.log('Real-time monitoring started successfully');
  //     return await Promise.resolve({
  //       success: true,
  //       message: 'Real-time monitoring started successfully',
  //       timestamp: this.dateTimeUtils.getUtcNow(),
  //     });
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  /**
   * Stop real-time monitoring
   * Requirements: 3.1 - Real-time connection status monitoring
   */
  // @Post('monitoring/stop')
  // @Roles('admin')
  // @RequireIPWhitelist('MONITORING_CONTROL')
  // async stopMonitoring(): Promise<SuccessResponse> {
  //   try {
  //     this.logger.log('Stopping real-time monitoring');

  //     this.brokerMonitoringService.stopRealTimeMonitoring();

  //     this.logger.log('Real-time monitoring stopped successfully');
  //     return await Promise.resolve({
  //       success: true,
  //       message: 'Real-time monitoring stopped successfully',
  //       timestamp: this.dateTimeUtils.getUtcNow(),
  //     });
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  // ==================== HEALTH MONITORING ENDPOINTS ====================

  /**
   * Get broker connection health status
   * Requirements: 3.4, 3.5, 5.7 - Health status and statistics retrieval
   */
  @Get('health')
  @Roles(RoleEnum.enum.admin)
  async getBrokerHealth(): Promise<BrokerHealthResponse[]> {
    this.logger.log('Retrieving broker health status for all accounts');

    const accounts = await this.brokerService.findAccountsWithFilters({});
    const healthResponses: BrokerHealthResponse[] = [];

    // Get health information for each account
    for (const account of accounts) {
      try {
        // TODO: Implement BrokerHealthService
        const connectionHealth: ConnectionHealth = {
          accountId: account.id.toString(),
          status: BrokerConnectionStatusEnum.enum.CONNECTED,
          lastCheckedAt: this.dateTimeUtils.getNewDate(),
          responseTime: 100,
          uptime: 100,
          failureCount: 0,
        };
        const statistics: ConnectionStatistics = {
          accountId: account.id.toString(),
          totalConnections: 0,
          successfulConnections: 0,
          failedConnections: 0,
          currentUptimeSeconds: 0,
          totalDowntimeSeconds: 0,
          successRate: 100,
          uptimePercentage: 100,
        };

        healthResponses.push({
          accountId: account.id.toString(),
          brokerType: account.brokerType,
          accountName: account.accountName,
          connectionHealth,
          statistics,
        });
      } catch (error) {
        this.logger.warn(`Failed to get health for account ${account.id}:`, error);
        // Continue with other accounts even if one fails
      }
    }

    this.logger.log(`Retrieved health status for ${healthResponses.length} broker accounts`);
    return healthResponses;
  }

  /**
   * Get health status for a specific broker account
   * Requirements: 3.4, 3.5 - Individual account health status
   */
  @Get('accounts/:id/health')
  @Roles(RoleEnum.enum.admin)
  async getBrokerAccountHealth(@Param() params: AccountIdParamDto): Promise<BrokerHealthResponse> {
    this.logger.log(`Retrieving health status for account: ${params.id}`);

    const account = await this.brokerService.findAccountById(params.id);
    if (!account) {
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, 'BROKER', {
        message: `Broker account with ID ${params.id} not found`,
        context: { accountId: params.id.toString() },
      });
    }

    // TODO: Implement BrokerHealthService
    const connectionHealth: ConnectionHealth = {
      accountId: params.id.toString(),
      status: BrokerConnectionStatusEnum.enum.CONNECTED,
      lastCheckedAt: this.dateTimeUtils.getNewDate(),
      responseTime: 100,
      uptime: 100,
      failureCount: 0,
    };
    const statistics: ConnectionStatistics = {
      accountId: params.id.toString(),
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      currentUptimeSeconds: 0,
      totalDowntimeSeconds: 0,
      successRate: 100,
      uptimePercentage: 100,
    };

    const healthResponse: BrokerHealthResponse = {
      accountId: params.id.toString(),
      brokerType: account.brokerType,
      accountName: account.accountName,
      connectionHealth,
      statistics,
    };

    this.logger.log(`Retrieved health status for account: ${params.id}`);
    return healthResponse;
  }

  /**
   * Trigger manual reconnection for a broker account
   * Requirements: 3.6, 5.8 - Manual reconnection functionality
   */
  @Post('reconnect/:id')
  @Roles(RoleEnum.enum.admin)
  reconnectBroker(@Param() params: AccountIdParamDto): ReconnectionResponse {
    this.logger.log(`Initiating manual reconnection for account: ${params.id}`);

    // TODO: Implement BrokerHealthService
    const reconnectionResult = {
      success: true,
      message: 'Reconnection initiated successfully',
      timestamp: this.dateTimeUtils.getNewDate(),
      newStatus: BrokerConnectionStatusEnum.enum.CONNECTED,
      errorMessage: undefined,
    };

    const response: ReconnectionResponse = {
      success: reconnectionResult.success,
      accountId: params.id.toString(),
      message: reconnectionResult.success
        ? `Reconnection successful for account ${params.id}`
        : `Reconnection failed for account ${params.id}: ${reconnectionResult.errorMessage}`,
      connectionStatus: reconnectionResult.newStatus,
      timestamp: reconnectionResult.timestamp,
    };

    this.logger.log(
      `Manual reconnection ${reconnectionResult.success ? 'successful' : 'failed'} for account: ${params.id}`,
    );
    return response;
  }

  // ==================== UTILITY ENDPOINTS ====================

  /**
   * Get supported broker types
   * Requirements: System information endpoint
   */
  @Get('supported-brokers')
  @Roles(RoleEnum.enum.admin, RoleEnum.enum.user)
  getSupportedBrokers(): { brokers: BrokerType[]; timestamp: string } {
    this.logger.log('Retrieving supported broker types');

    const supportedBrokers: BrokerType[] = ['ZERODHA', 'ALICE_BLUE', 'FINVASIA', 'UPSTOX', 'DHAN', 'GROW', 'ANGEL_ONE'];

    return {
      brokers: supportedBrokers,
      timestamp: this.dateTimeUtils.getUtcNow(),
    };
  }

  /**
   * Get broker account statistics summary
   * Requirements: Admin dashboard information
   */
  @Get('statistics')
  @Roles(RoleEnum.enum.admin)
  async getBrokerStatistics(): Promise<{
    totalAccounts: number;
    activeAccounts: number;
    connectedAccounts: number;
    errorAccounts: number;
    brokerBreakdown: Record<BrokerType, number>;
    timestamp: string;
  }> {
    this.logger.log('Retrieving broker statistics');

    const allAccounts = await this.brokerService.findAccountsWithFilters({});
    // TODO: Implement BrokerHealthService
    const healthChecks: ConnectionHealth[] = allAccounts.map((account) => ({
      accountId: account.id.toString(),
      status: BrokerConnectionStatusEnum.enum.CONNECTED,
      lastCheckedAt: this.dateTimeUtils.getNewDate(),
      responseTime: 100,
      uptime: 100,
      failureCount: 0,
    }));

    let activeAccounts = 0;
    let connectedAccounts = 0;
    const errorAccounts = 0;
    const brokerBreakdown: Record<string, number> = {};

    for (let i = 0; i < allAccounts.length; i++) {
      const account = allAccounts[i];
      const health = healthChecks[i];

      if (
        health.status === BrokerConnectionStatusEnum.enum.CONNECTED ||
        health.status === BrokerConnectionStatusEnum.enum.CONNECTING ||
        health.status === BrokerConnectionStatusEnum.enum.RECONNECTING
      ) {
        activeAccounts++;
      }
      if (health.status === BrokerConnectionStatusEnum.enum.CONNECTED) {
        connectedAccounts++;
      }
      // TODO: Implement proper error detection logic
      // For now, assume no errors since we're using placeholder data
      // if (hasConnectionError) {
      //   errorAccounts++;
      // }

      brokerBreakdown[account.brokerType] = (brokerBreakdown[account.brokerType] || 0) + 1;
    }

    const statistics = {
      totalAccounts: allAccounts.length,
      activeAccounts,
      connectedAccounts,
      errorAccounts,
      brokerBreakdown: brokerBreakdown as Record<BrokerType, number>,
      timestamp: this.dateTimeUtils.getUtcNow(),
    };

    this.logger.log(`Retrieved broker statistics: ${JSON.stringify(statistics)}`);
    return statistics;
  }

  /**
   * Get client IP address from request
   * @private
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}
