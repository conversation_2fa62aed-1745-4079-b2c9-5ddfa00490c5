import { Test, TestingModule } from '@nestjs/testing';
import { DatastoreController } from './datastore.controller';
import { DatastoreService } from './datastore.service';

describe('DatastoreController', () => {
  let datastoreController: DatastoreController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [DatastoreController],
      providers: [DatastoreService],
    }).compile();

    datastoreController = app.get<DatastoreController>(DatastoreController);
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(datastoreController.getHello()).toBe('Hello World!');
    });
  });
});
