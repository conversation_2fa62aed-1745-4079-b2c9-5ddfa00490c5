import { Module } from '@nestjs/common';
import { DatastoreController } from './datastore.controller';
import { DatastoreService } from './datastore.service';
import { SymbolController } from './symbol.controller';
import { SymbolMicroservice } from './symbol.microservice';

// Core modules
import { CoreModule } from '@app/core';
import { CommonModule } from '@app/common';
import { UtilsModule } from '@app/utils';

// Symbol module
import { SymbolModule } from '@app/symbol';

// Health check
import { HealthCheckController } from './health-check.controller';

/**
 * Datastore Application Module
 *
 * Provides data storage and retrieval services for the PatternTrade API ecosystem.
 * Includes symbol master data management, audit logging, and microservice communication.
 *
 * Features:
 * - Symbol master data storage and retrieval
 * - QuestDB integration for time-series data
 * - PostgreSQL integration for audit logs
 * - Redis transport for microservice communication
 * - Comprehensive health checks
 * - Background job processing with BullMQ
 */
@Module({
  imports: [
    // Core infrastructure modules
    CoreModule,
    CommonModule,
    UtilsModule,

    // Business logic modules
    SymbolModule,
  ],
  controllers: [
    DatastoreController,
    SymbolController,
    HealthCheckController,
  ],
  providers: [
    DatastoreService,
    SymbolMicroservice,
  ],
  exports: [
    DatastoreService,
    SymbolMicroservice,
  ],
})
export class DatastoreModule {}
