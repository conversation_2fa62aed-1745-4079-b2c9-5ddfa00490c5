import { Controller, Get, Logger } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { DatastoreService } from './datastore.service';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

/**
 * Datastore Controller
 *
 * Main controller for the datastore application providing both HTTP endpoints
 * and microservice message handlers for general datastore operations.
 */
@Controller()
export class DatastoreController {
  private readonly logger = new Logger(DatastoreController.name);

  constructor(
    private readonly datastoreService: DatastoreService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== HTTP ENDPOINTS ====================

  /**
   * Basic service information endpoint
   */
  @Get()
  getHello(): string {
    return this.datastoreService.getHello();
  }

  /**
   * Get comprehensive service information
   */
  @Get('info')
  async getServiceInfo() {
    try {
      const info = await this.datastoreService.getServiceInfo();
      return {
        success: true,
        data: info,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get service info', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get service status
   */
  @Get('status')
  async getServiceStatus() {
    try {
      const status = await this.datastoreService.getServiceStatus();
      return {
        success: true,
        data: status,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get service status', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== MICROSERVICE MESSAGE HANDLERS ====================

  /**
   * Handle service info request from other microservices
   */
  @MessagePattern('datastore.info')
  async handleServiceInfoRequest() {
    try {
      this.logger.log('Service info request received');

      const info = await this.datastoreService.getServiceInfo();

      return {
        success: true,
        data: info,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle service info request', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        success: false,
        error: this.errorUtils.getErrorMessage(error),
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  /**
   * Handle service status request from other microservices
   */
  @MessagePattern('datastore.status')
  async handleServiceStatusRequest() {
    try {
      this.logger.log('Service status request received');

      const status = await this.datastoreService.getServiceStatus();

      return {
        success: true,
        data: status,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle service status request', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        success: false,
        error: this.errorUtils.getErrorMessage(error),
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    }
  }
}
