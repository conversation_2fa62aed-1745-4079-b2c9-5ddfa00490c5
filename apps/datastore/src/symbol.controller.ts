import { Controller, Get, Post, Body, Param, Query, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import {
  SymbolService,
  SymbolMasterRepository,
  SymbolDownloadQueueService,
  SymbolAuditService,
  SymbolMasterQueryFilters,
  SymbolDownloadRequest,
  SymbolServiceMessagePatterns,
  SymbolServiceSchemas,
} from '@app/symbol';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

/**
 * Symbol Controller for Datastore Application
 *
 * Provides both HTTP endpoints and microservice message handlers for symbol operations.
 * Handles symbol master data management, download operations, and audit queries.
 *
 * Features:
 * - HTTP REST API for direct access
 * - Redis microservice message patterns for inter-service communication
 * - Comprehensive error handling and logging
 * - Health checks and status monitoring
 */
@Controller('symbols')
export class SymbolController {
  private readonly logger = new Logger(SymbolController.name);

  constructor(
    private readonly symbolService: SymbolService,
    private readonly symbolRepository: SymbolMasterRepository,
    private readonly symbolQueueService: SymbolDownloadQueueService,
    private readonly symbolAuditService: SymbolAuditService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== HTTP REST API ENDPOINTS ====================

  /**
   * Get symbols with filters and pagination
   */
  @Get()
  async getSymbols(@Query() filters: SymbolMasterQueryFilters) {
    try {
      this.logger.log('Getting symbols with filters', filters);

      const result = await this.symbolRepository.findSymbols(filters);

      return {
        success: true,
        data: result.data,
        meta: {
          total: result.total,
          limit: filters.limit || 100,
          offset: filters.offset || 0,
          hasMore: result.hasMore,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbols', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get symbol by instrument token
   */
  @Get(':instrumentToken')
  async getSymbolByToken(@Param('instrumentToken') instrumentToken: string) {
    try {
      this.logger.log('Getting symbol by token', { instrumentToken });

      const symbol = await this.symbolRepository.findByInstrumentToken(instrumentToken);

      return {
        success: true,
        data: symbol,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get symbol by token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Search symbols by trading symbol pattern
   */
  @Get('search/:searchTerm')
  async searchSymbols(
    @Param('searchTerm') searchTerm: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    try {
      this.logger.log('Searching symbols', { searchTerm, limit, offset });

      const symbols = await this.symbolRepository.searchSymbols(searchTerm, limit || 50, offset || 0);

      return {
        success: true,
        data: symbols,
        meta: {
          searchTerm,
          limit: limit || 50,
          offset: offset || 0,
          count: symbols.length,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Trigger symbol download
   */
  @Post('download')
  async triggerDownload(@Body() request: SymbolDownloadRequest) {
    try {
      this.logger.log('Triggering symbol download', request);

      const result = await this.symbolQueueService.addSymbolDownloadJob(request.exchange, request.segment, {
        forceRefresh: request.forceRefresh,
        batchSize: request.batchSize,
        requestId: request.requestId,
        priority: 1, // High priority for manual downloads
      });

      return {
        success: true,
        data: {
          jobId: result.jobId,
          requestId: result.requestId,
          scheduledAt: this.dateTimeUtils.getUtcNow(),
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger symbol download', {
        request,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get download job status
   */
  @Get('download/status/:jobId')
  async getDownloadStatus(@Param('jobId') jobId: string) {
    try {
      this.logger.log('Getting download status', { jobId });

      const status = await this.symbolQueueService.getJobStatus(jobId);

      if (!status) {
        return {
          success: false,
          error: 'Job not found',
          timestamp: this.dateTimeUtils.getUtcNow(),
        };
      }

      return {
        success: true,
        data: status,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get download status', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get repository statistics
   */
  @Get('stats/repository')
  async getRepositoryStats() {
    try {
      this.logger.log('Getting repository statistics');

      const stats = await this.symbolRepository.getStats();

      return {
        success: true,
        data: stats,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get repository statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get queue statistics
   */
  @Get('stats/queue')
  async getQueueStats() {
    try {
      this.logger.log('Getting queue statistics');

      const stats = await this.symbolQueueService.getQueueStats();

      return {
        success: true,
        data: stats,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get queue statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get audit logs by request ID
   */
  @Get('audit/:requestId')
  async getAuditLogs(@Param('requestId') requestId: string) {
    try {
      this.logger.log('Getting audit logs', { requestId });

      const logs = await this.symbolAuditService.getAuditLogsByRequestId(requestId);

      return {
        success: true,
        data: logs,
        meta: {
          requestId,
          count: logs.length,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to get audit logs', {
        requestId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== MICROSERVICE MESSAGE HANDLERS ====================

  /**
   * Handle get symbols request from other microservices
   */
  @MessagePattern(SymbolServiceMessagePatterns.GET_SYMBOLS)
  async handleGetSymbols(@Payload() filters: SymbolMasterQueryFilters) {
    try {
      this.logger.log('Handling microservice get symbols request', filters);

      const result = await this.symbolRepository.findSymbols(filters);

      return {
        success: true,
        data: result.data,
        meta: {
          total: result.total,
          limit: filters.limit || 100,
          offset: filters.offset || 0,
          hasMore: result.hasMore,
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle get symbols request', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Handle get symbol by token request from other microservices
   */
  @MessagePattern(SymbolServiceMessagePatterns.GET_SYMBOL_BY_TOKEN)
  async handleGetSymbolByToken(@Payload() payload: { instrumentToken: string }) {
    try {
      this.logger.log('Handling microservice get symbol by token request', payload);

      const symbol = await this.symbolRepository.findByInstrumentToken(payload.instrumentToken);

      return {
        success: true,
        data: symbol,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle get symbol by token request', {
        payload,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Handle trigger download request from other microservices
   */
  @MessagePattern(SymbolServiceMessagePatterns.TRIGGER_DOWNLOAD)
  async handleTriggerDownload(@Payload() request: SymbolDownloadRequest) {
    try {
      this.logger.log('Handling microservice trigger download request', request);

      const result = await this.symbolQueueService.addSymbolDownloadJob(request.exchange, request.segment, {
        forceRefresh: request.forceRefresh,
        batchSize: request.batchSize,
        requestId: request.requestId,
        priority: request.priority || 5,
      });

      return {
        success: true,
        data: {
          jobId: result.jobId,
          requestId: result.requestId,
          scheduledAt: this.dateTimeUtils.getUtcNow(),
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle trigger download request', {
        request,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Handle get download status request from other microservices
   */
  @MessagePattern(SymbolServiceMessagePatterns.GET_DOWNLOAD_STATUS)
  async handleGetDownloadStatus(@Payload() payload: { jobId: string }) {
    try {
      this.logger.log('Handling microservice get download status request', payload);

      const status = await this.symbolQueueService.getJobStatus(payload.jobId);

      return {
        success: true,
        data: status,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle get download status request', {
        payload,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Handle health check request from other microservices
   */
  @MessagePattern(SymbolServiceMessagePatterns.GET_HEALTH)
  async handleGetHealth() {
    try {
      this.logger.log('Handling microservice health check request');

      const [repositoryHealth, queueHealth, auditHealth] = await Promise.all([
        this.symbolRepository.getHealthStatus(),
        this.symbolQueueService.getComprehensiveHealthStatus(),
        this.symbolAuditService.getHealthStatus(),
      ]);

      const isHealthy = repositoryHealth.isHealthy && queueHealth.isHealthy && auditHealth.isHealthy;

      return {
        success: true,
        data: {
          isHealthy,
          services: {
            repository: repositoryHealth,
            queue: queueHealth,
            audit: auditHealth,
          },
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Failed to handle health check request', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        success: false,
        data: {
          isHealthy: false,
          error: this.errorUtils.getErrorMessage(error),
        },
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    }
  }
}
