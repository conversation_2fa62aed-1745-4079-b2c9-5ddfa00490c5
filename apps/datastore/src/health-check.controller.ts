import { Controller, Get, Logger } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { SymbolMicroservice } from './symbol.microservice';
import { HealthCheckService } from '@app/core/health-check';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

/**
 * Health Check Controller for Datastore Application
 * 
 * Provides comprehensive health monitoring for the datastore application
 * including all symbol-related services, database connections, and
 * background job processing systems.
 * 
 * Features:
 * - Overall application health status
 * - Individual service health checks
 * - Database connectivity monitoring
 * - Queue system health monitoring
 * - Microservice communication health
 */
@Controller('health')
export class HealthCheckController {
  private readonly logger = new Logger(HealthCheckController.name);

  constructor(
    private readonly symbolMicroservice: SymbolMicroservice,
    private readonly healthCheckService: HealthCheckService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== HTTP HEALTH CHECK ENDPOINTS ====================

  /**
   * Basic health check endpoint
   */
  @Get()
  async getHealth() {
    try {
      this.logger.log('Health check requested');

      const appStatus = this.healthCheckService.getAppStatus();
      const detailedStatus = this.healthCheckService.getDetailedHealthStatus();

      return {
        status: appStatus ? 'healthy' : 'unhealthy',
        timestamp: this.dateTimeUtils.getUtcNow(),
        services: {
          postgres: detailedStatus.postgresStatus,
          questdb: detailedStatus.questdbStatus,
          queue: detailedStatus.queueStatus,
        },
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
      };
    } catch (error) {
      this.logger.error('Health check failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        status: 'unhealthy',
        timestamp: this.dateTimeUtils.getUtcNow(),
        error: this.errorUtils.getErrorMessage(error),
      };
    }
  }

  /**
   * Detailed health check with comprehensive service status
   */
  @Get('detailed')
  async getDetailedHealth() {
    try {
      this.logger.log('Detailed health check requested');

      const [appHealth, symbolHealth] = await Promise.all([
        this.healthCheckService.getDetailedHealthStatus(),
        this.symbolMicroservice.getHealthStatus(),
      ]);

      const overallStatus = appHealth.appStatus && symbolHealth.isHealthy;

      return {
        status: overallStatus ? 'healthy' : 'unhealthy',
        timestamp: this.dateTimeUtils.getUtcNow(),
        application: {
          status: appHealth.appStatus,
          uptime: process.uptime(),
          version: process.env.npm_package_version || '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          pid: process.pid,
          memory: process.memoryUsage(),
        },
        infrastructure: {
          postgres: {
            status: appHealth.postgresStatus,
            lastCheck: appHealth.services.postgres.lastCheck,
          },
          questdb: {
            status: appHealth.questdbStatus,
            lastCheck: appHealth.services.questdb.lastCheck,
            connectionStats: appHealth.services.questdb.connectionStats,
          },
          queues: {
            status: appHealth.queueStatus,
            lastCheck: appHealth.services.queues.lastCheck,
            summary: appHealth.services.queues.summary,
            queues: appHealth.services.queues.queues,
            workers: appHealth.services.queues.workers,
          },
        },
        services: {
          symbol: symbolHealth,
        },
        issues: symbolHealth.issues,
      };
    } catch (error) {
      this.logger.error('Detailed health check failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        status: 'unhealthy',
        timestamp: this.dateTimeUtils.getUtcNow(),
        error: this.errorUtils.getErrorMessage(error),
        services: {
          symbol: {
            isHealthy: false,
            issues: ['Health check failed'],
          },
        },
      };
    }
  }

  /**
   * Symbol service specific health check
   */
  @Get('symbol')
  async getSymbolHealth() {
    try {
      this.logger.log('Symbol service health check requested');

      const symbolHealth = await this.symbolMicroservice.getHealthStatus();

      return {
        status: symbolHealth.isHealthy ? 'healthy' : 'unhealthy',
        timestamp: this.dateTimeUtils.getUtcNow(),
        ...symbolHealth,
      };
    } catch (error) {
      this.logger.error('Symbol health check failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        status: 'unhealthy',
        timestamp: this.dateTimeUtils.getUtcNow(),
        isHealthy: false,
        error: this.errorUtils.getErrorMessage(error),
        issues: ['Symbol health check failed'],
      };
    }
  }

  /**
   * Service statistics endpoint
   */
  @Get('stats')
  async getServiceStats() {
    try {
      this.logger.log('Service statistics requested');

      const stats = await this.symbolMicroservice.getServiceStatistics();

      return {
        success: true,
        timestamp: this.dateTimeUtils.getUtcNow(),
        data: stats,
      };
    } catch (error) {
      this.logger.error('Failed to get service statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        success: false,
        timestamp: this.dateTimeUtils.getUtcNow(),
        error: this.errorUtils.getErrorMessage(error),
      };
    }
  }

  /**
   * Readiness probe for Kubernetes/Docker
   */
  @Get('ready')
  async getReadiness() {
    try {
      const symbolHealth = await this.symbolMicroservice.getHealthStatus();
      const appHealth = this.healthCheckService.getAppStatus();

      const isReady = appHealth && symbolHealth.isHealthy;

      if (isReady) {
        return {
          status: 'ready',
          timestamp: this.dateTimeUtils.getUtcNow(),
        };
      } else {
        return {
          status: 'not ready',
          timestamp: this.dateTimeUtils.getUtcNow(),
          issues: symbolHealth.issues,
        };
      }
    } catch (error) {
      this.logger.error('Readiness check failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        status: 'not ready',
        timestamp: this.dateTimeUtils.getUtcNow(),
        error: this.errorUtils.getErrorMessage(error),
      };
    }
  }

  /**
   * Liveness probe for Kubernetes/Docker
   */
  @Get('live')
  async getLiveness() {
    try {
      // Basic liveness check - just ensure the service is responding
      return {
        status: 'alive',
        timestamp: this.dateTimeUtils.getUtcNow(),
        uptime: process.uptime(),
        pid: process.pid,
      };
    } catch (error) {
      this.logger.error('Liveness check failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        status: 'dead',
        timestamp: this.dateTimeUtils.getUtcNow(),
        error: this.errorUtils.getErrorMessage(error),
      };
    }
  }

  // ==================== MICROSERVICE HEALTH CHECK HANDLERS ====================

  /**
   * Handle health check request from other microservices
   */
  @MessagePattern('datastore.health')
  async handleHealthCheck() {
    try {
      this.logger.log('Microservice health check requested');

      const [appHealth, symbolHealth] = await Promise.all([
        this.healthCheckService.getDetailedHealthStatus(),
        this.symbolMicroservice.getHealthStatus(),
      ]);

      const overallStatus = appHealth.appStatus && symbolHealth.isHealthy;

      return {
        success: true,
        data: {
          status: overallStatus ? 'healthy' : 'unhealthy',
          isHealthy: overallStatus,
          timestamp: this.dateTimeUtils.getUtcNow(),
          services: {
            application: appHealth.appStatus,
            symbol: symbolHealth.isHealthy,
          },
          issues: symbolHealth.issues,
        },
      };
    } catch (error) {
      this.logger.error('Microservice health check failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        success: false,
        data: {
          status: 'unhealthy',
          isHealthy: false,
          timestamp: this.dateTimeUtils.getUtcNow(),
          error: this.errorUtils.getErrorMessage(error),
        },
      };
    }
  }

  /**
   * Handle service statistics request from other microservices
   */
  @MessagePattern('datastore.stats')
  async handleStatsRequest() {
    try {
      this.logger.log('Microservice stats request received');

      const stats = await this.symbolMicroservice.getServiceStatistics();

      return {
        success: true,
        data: stats,
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    } catch (error) {
      this.logger.error('Microservice stats request failed', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        success: false,
        error: this.errorUtils.getErrorMessage(error),
        timestamp: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  /**
   * Handle ping request from other microservices
   */
  @MessagePattern('datastore.ping')
  async handlePing() {
    return {
      success: true,
      data: {
        pong: true,
        timestamp: this.dateTimeUtils.getUtcNow(),
        service: 'datastore',
        version: process.env.npm_package_version || '1.0.0',
      },
    };
  }
}
